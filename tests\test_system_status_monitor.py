#!/usr/bin/env python3
"""
Unit Tests for System Status Monitor

Tests:
- System status monitor initialization
- Status update generation
- Alert condition checking
- Performance metrics calculation
- Trade tracking functionality
- System health monitoring
"""

import pytest
import asyncio
import tempfile
import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Import the system status monitor
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.system_status_monitor import (
    SystemStatusMonitor, 
    SystemStatus, 
    TradeRecord
)


class TestSystemStatusMonitor:
    """Test cases for System Status Monitor"""
    
    @pytest.fixture
    async def monitor(self):
        """Create a test monitor instance"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            monitor = SystemStatusMonitor(str(config_path))
            await monitor.initialize(initial_balance=100000.0, daily_trades_target=50)
            yield monitor
            await monitor.cleanup()
    
    @pytest.mark.asyncio
    async def test_monitor_initialization(self):
        """Test monitor initialization"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            monitor = SystemStatusMonitor(str(config_path))
            
            # Test initialization
            result = await monitor.initialize(
                initial_balance=100000.0,
                daily_trades_target=50
            )
            
            assert result is True
            assert monitor.initial_balance == 100000.0
            assert monitor.current_balance == 100000.0
            assert monitor.daily_trades_target == 50
            
            await monitor.cleanup()
    
    @pytest.mark.asyncio
    async def test_add_trade(self, monitor):
        """Test adding trades"""
        # Create a test trade
        trade = TradeRecord(
            trade_id="TEST001",
            timestamp=datetime.now().isoformat(),
            symbol="NIFTY24800CE",
            action="BUY",
            quantity=1,
            entry_price=150.0,
            exit_price=None,
            pnl=None,
            status="OPEN",
            strategy="test_strategy",
            confidence=0.75
        )
        
        # Add trade
        monitor.add_trade(trade)
        
        # Verify trade was added
        assert len(monitor.trades_today) == 1
        assert len(monitor.active_positions) == 1
        assert monitor.trades_today[0].trade_id == "TEST001"
        assert monitor.active_positions[0].symbol == "NIFTY24800CE"
    
    @pytest.mark.asyncio
    async def test_close_trade(self, monitor):
        """Test closing trades"""
        # Add an open trade
        trade = TradeRecord(
            trade_id="TEST001",
            timestamp=datetime.now().isoformat(),
            symbol="NIFTY24800CE",
            action="BUY",
            quantity=1,
            entry_price=150.0,
            exit_price=None,
            pnl=None,
            status="OPEN",
            strategy="test_strategy",
            confidence=0.75
        )
        monitor.add_trade(trade)
        
        # Close the trade
        monitor.update_trade("TEST001", {
            "status": "CLOSED",
            "exit_price": 160.0,
            "pnl": 10.0
        })
        
        # Verify trade was closed
        assert len(monitor.active_positions) == 0
        assert len(monitor.closed_trades_today) == 1
        assert monitor.closed_trades_today[0].pnl == 10.0
    
    @pytest.mark.asyncio
    async def test_balance_updates(self, monitor):
        """Test balance update functionality"""
        # Update balance
        monitor.update_balance(105000.0)
        assert monitor.current_balance == 105000.0
        
        # Update used balance
        monitor.update_used_balance(15000.0)
        assert monitor.used_balance == 15000.0
    
    @pytest.mark.asyncio
    async def test_daily_win_rate_calculation(self, monitor):
        """Test daily win rate calculation"""
        # Add winning trades
        for i in range(3):
            trade = TradeRecord(
                trade_id=f"WIN{i:03d}",
                timestamp=datetime.now().isoformat(),
                symbol="NIFTY24800CE",
                action="BUY",
                quantity=1,
                entry_price=150.0,
                exit_price=160.0,
                pnl=10.0,
                status="CLOSED",
                strategy="test_strategy",
                confidence=0.75
            )
            monitor.add_trade(trade)
        
        # Add losing trades
        for i in range(2):
            trade = TradeRecord(
                trade_id=f"LOSS{i:03d}",
                timestamp=datetime.now().isoformat(),
                symbol="NIFTY24800PE",
                action="BUY",
                quantity=1,
                entry_price=150.0,
                exit_price=140.0,
                pnl=-10.0,
                status="CLOSED",
                strategy="test_strategy",
                confidence=0.75
            )
            monitor.add_trade(trade)
        
        # Calculate win rate
        win_rate = monitor._calculate_daily_win_rate()
        assert win_rate == 60.0  # 3 wins out of 5 trades = 60%
    
    @pytest.mark.asyncio
    async def test_best_worst_trade_calculation(self, monitor):
        """Test best and worst trade calculation"""
        # Add trades with different P&L
        trades_data = [
            ("TRADE001", 25.0),
            ("TRADE002", -15.0),
            ("TRADE003", 10.0),
            ("TRADE004", -5.0),
            ("TRADE005", 30.0)
        ]
        
        for trade_id, pnl in trades_data:
            trade = TradeRecord(
                trade_id=trade_id,
                timestamp=datetime.now().isoformat(),
                symbol="NIFTY24800CE",
                action="BUY",
                quantity=1,
                entry_price=150.0,
                exit_price=150.0 + pnl,
                pnl=pnl,
                status="CLOSED",
                strategy="test_strategy",
                confidence=0.75
            )
            monitor.add_trade(trade)
        
        # Test best and worst trade calculation
        best_trade = monitor._get_best_trade_today()
        worst_trade = monitor._get_worst_trade_today()
        
        assert best_trade == 30.0
        assert worst_trade == -15.0
    
    @pytest.mark.asyncio
    async def test_system_metrics_mock(self, monitor):
        """Test system metrics with mocked psutil"""
        with patch('agents.system_status_monitor.psutil') as mock_psutil:
            # Mock psutil functions
            mock_psutil.virtual_memory.return_value.used = 500 * 1024 * 1024  # 500 MB
            mock_psutil.cpu_percent.return_value = 25.0
            
            memory_usage, cpu_usage = await monitor._get_system_metrics()
            
            assert memory_usage == 500.0  # MB
            assert cpu_usage == 25.0
    
    @pytest.mark.asyncio
    async def test_system_metrics_no_psutil(self, monitor):
        """Test system metrics without psutil"""
        with patch('agents.system_status_monitor.psutil', side_effect=ImportError):
            memory_usage, cpu_usage = await monitor._get_system_metrics()
            
            # Should return default values
            assert memory_usage == 500.0
            assert cpu_usage == 25.0
    
    @pytest.mark.asyncio
    async def test_alert_conditions(self, monitor):
        """Test alert condition checking"""
        # Create a status with alert conditions
        status = SystemStatus(
            timestamp=datetime.now().isoformat(),
            current_balance=95000.0,  # 5% loss
            used_balance=10000.0,
            available_balance=85000.0,
            total_pnl_real=-5000.0,
            total_pnl_estimated=-5000.0,
            total_pnl_percentage=-5.0,  # Triggers alert
            daily_trades_completed=45,
            daily_trades_target=50,
            active_positions=3,
            max_active_positions=5,
            system_uptime="01:30:00",
            trading_mode="PAPER",
            last_trade_time=datetime.now().isoformat(),
            daily_win_rate=35.0,  # Below threshold
            daily_best_trade=100.0,
            daily_worst_trade=-200.0,
            memory_usage_mb=1200.0,  # Above threshold
            cpu_usage_percent=85.0  # Above threshold
        )
        
        # Mock the notification method
        monitor._send_notification = AsyncMock()
        
        # Check alert conditions
        await monitor._check_alert_conditions(status)
        
        # Verify notifications were sent (at least one alert should trigger)
        assert monitor._send_notification.call_count > 0
    
    @pytest.mark.asyncio
    async def test_status_file_saving(self, monitor):
        """Test status file saving"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock the data directory
            monitor.config = {
                'status_update_interval': 200,
                'daily_trades_target': 50,
                'alert_thresholds': {
                    'max_daily_loss_percent': 5.0,
                    'min_win_rate': 40.0,
                    'max_drawdown_percent': 10.0,
                    'memory_usage_mb': 1000,
                    'cpu_usage_percent': 80.0
                }
            }
            
            # Create a test status
            status = SystemStatus(
                timestamp=datetime.now().isoformat(),
                current_balance=100000.0,
                used_balance=0.0,
                available_balance=100000.0,
                total_pnl_real=0.0,
                total_pnl_estimated=0.0,
                total_pnl_percentage=0.0,
                daily_trades_completed=0,
                daily_trades_target=50,
                active_positions=0,
                max_active_positions=5,
                system_uptime="00:00:00",
                trading_mode="PAPER",
                last_trade_time=None,
                daily_win_rate=0.0,
                daily_best_trade=0.0,
                daily_worst_trade=0.0,
                memory_usage_mb=500.0,
                cpu_usage_percent=25.0
            )
            
            # Mock the Path to use temp directory
            with patch('agents.system_status_monitor.Path') as mock_path:
                status_file = Path(temp_dir) / "system_status.json"
                mock_path.return_value = status_file
                
                # Save status
                await monitor._save_status_to_file(status)
                
                # Verify file was created and contains correct data
                assert status_file.exists()
                with open(status_file, 'r') as f:
                    saved_data = json.load(f)
                    assert saved_data['current_balance'] == 100000.0
                    assert saved_data['trading_mode'] == "PAPER"
    
    @pytest.mark.asyncio
    async def test_monitor_start_stop(self, monitor):
        """Test monitor start and stop functionality"""
        # Start monitor
        result = await monitor.start()
        assert result is True
        assert monitor.is_running is True
        
        # Stop monitor
        await monitor.cleanup()
        assert monitor.is_running is False


@pytest.mark.asyncio
async def test_monitor_integration():
    """Integration test for system status monitor"""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = Path(temp_dir) / "test_config.yaml"
        monitor = SystemStatusMonitor(str(config_path))
        
        try:
            # Initialize
            await monitor.initialize(initial_balance=100000.0)
            
            # Add some test trades
            for i in range(5):
                trade = TradeRecord(
                    trade_id=f"TRADE{i:03d}",
                    timestamp=datetime.now().isoformat(),
                    symbol=f"NIFTY2480{i}CE",
                    action="BUY",
                    quantity=1,
                    entry_price=150.0,
                    exit_price=155.0 if i % 2 == 0 else 145.0,
                    pnl=5.0 if i % 2 == 0 else -5.0,
                    status="CLOSED",
                    strategy="test_strategy",
                    confidence=0.75
                )
                monitor.add_trade(trade)
            
            # Update balance
            monitor.update_balance(100000.0)
            
            # Generate status update (without starting the full monitor)
            await monitor._generate_status_update()
            
            # Verify status was generated
            assert monitor.current_portfolio_pnl is not None
            
        finally:
            await monitor.cleanup()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
